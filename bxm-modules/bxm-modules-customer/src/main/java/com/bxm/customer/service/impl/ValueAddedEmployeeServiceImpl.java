package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.customer.converter.ValueAddedEmployeeConverter;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.ValueAddedFile;
import com.bxm.customer.domain.dto.FileStatusInfo;
import com.bxm.customer.domain.dto.valueAdded.PersonalTaxDetailImportDTO;
import com.bxm.customer.domain.dto.valueAdded.SocialInsuranceDTO;
import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.domain.enums.ValueAddedEmployeeStatus;
import com.bxm.customer.domain.enums.ValueAddedEntryType;
import com.bxm.customer.domain.enums.ValueAddedOperationType;
import com.bxm.customer.domain.enums.ValueAddedProcessStatus;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;
import com.bxm.customer.mapper.ValueAddedEmployeeMapper;
import com.bxm.customer.service.IValueAddedEmployeeService;
import com.bxm.customer.service.IValueAddedFileService;
import com.bxm.customer.service.strategy.ValueAddedEmployeeStrategyFactory;
import com.bxm.customer.service.strategy.ValueAddedEmployeeUpsertStrategy;
import com.bxm.file.api.RemoteFileService;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 增值员工信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Service
public class ValueAddedEmployeeServiceImpl extends ServiceImpl<ValueAddedEmployeeMapper, ValueAddedEmployee>
        implements IValueAddedEmployeeService {

    @Autowired
    private ValueAddedEmployeeStrategyFactory strategyFactory;

    @Autowired
    private ValueAddedEmployeeConverter converter;

    @Autowired
    private IValueAddedFileService valueAddedFileService;

    @Autowired
    private RemoteFileService remoteFileService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean upsert(ValueAddedEmployeeVO employeeVO) {
        try {
            log.info("Starting upsert operation for employee: {}, bizType: {}", employeeVO.getEmployeeName(), employeeVO.getBizType());

            // 1. 基础验证（@Valid注解已处理基础字段验证）
            validateEmployee(employeeVO);

            // 2. VO转换为DO
            ValueAddedEmployee employee = converter.voToDo(employeeVO);

            // 3. 获取对应的业务策略
            ValueAddedEmployeeUpsertStrategy strategy = strategyFactory.getStrategy(employee.getBizType());

            // 4. 业务特定字段验证
            strategy.validateBusinessFields(employee);

            // 5. 预处理
            strategy.preprocessEmployee(employee);

            // 6. 查找现有记录
            ValueAddedEmployee existingEmployee = strategy.findExistingEmployee(employee);

            boolean isUpdate = existingEmployee != null;
            ValueAddedEmployee targetEmployee;

            if (isUpdate) {
                // 更新操作：合并数据
                targetEmployee = strategy.mergeEmployee(existingEmployee, employee);

                // 执行更新
                if (!updateById(targetEmployee)) {
                    throw new RuntimeException("Failed to update employee: " + targetEmployee.getId());
                }
            } else {
                // 插入操作
                targetEmployee = employee;

                // 执行插入
                if (!save(targetEmployee)) {
                    throw new RuntimeException("Failed to insert employee: " + targetEmployee.getEmployeeName());
                }
            }

            // 7. 后处理
            strategy.postprocessEmployee(targetEmployee, isUpdate);

            log.info("Upsert operation completed successfully for employee: {}, operation: {}", targetEmployee.getEmployeeName(), isUpdate ? "UPDATE" : "INSERT");

            return true;

        } catch (IllegalArgumentException e) {
            log.error("Validation failed during upsert operation for employee: {}, bizType: {}, error: {}", employeeVO.getEmployeeName(), employeeVO.getBizType(), e.getMessage());
            throw e;
        } catch (RuntimeException e) {
            log.error("Runtime error during upsert operation for employee: {}, bizType: {}, error: {}", employeeVO.getEmployeeName(), employeeVO.getBizType(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during upsert operation for employee: {}, bizType: {}, error: {}", employeeVO.getEmployeeName(), employeeVO.getBizType(), e.getMessage(), e);
            throw new RuntimeException("Upsert operation failed for employee " + employeeVO.getEmployeeName() + ": " + e.getMessage(), e);
        }
    }

    @Override
    public ValueAddedEmployee getByDeliveryOrderAndIdNumber(String deliveryOrderNo, String idNumber, Integer bizType) {
        if (StringUtils.isEmpty(deliveryOrderNo) || StringUtils.isEmpty(idNumber) || bizType == null) {
            return null;
        }

        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                .eq(ValueAddedEmployee::getIdNumber, idNumber.trim())
                .eq(ValueAddedEmployee::getBizType, bizType);

        return getOne(queryWrapper);
    }

    @Override
    public void validateEmployee(ValueAddedEmployeeVO employeeVO) {
        if (employeeVO == null) {
            throw new IllegalArgumentException("Employee VO cannot be null");
        }
        // 验证业务类型是否有效
        if (!ValueAddedBizType.isValid(employeeVO.getBizType())) {
            throw new IllegalArgumentException("Invalid business type: " + employeeVO.getBizType());
        }
    }

    @Override
    public List<SocialInsuranceDTO> getSocialInsuranceDetailForExport(String deliveryOrderNo) {
        log.info("Getting social insurance detail for export, deliveryOrderNo: {}", deliveryOrderNo);

        // 查询社保业务类型的员工数据
        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                   .eq(ValueAddedEmployee::getBizType, ValueAddedBizType.SOCIAL_INSURANCE.getCode()); // 社医保

        List<ValueAddedEmployee> employees = this.list(queryWrapper);
        List<SocialInsuranceDTO> exportList = new ArrayList<>();

        for (ValueAddedEmployee employee : employees) {
            SocialInsuranceDTO exportDTO = SocialInsuranceDTO.builder()
                    .operationType(convertOperationType(employee.getOperationType()))
                    .employeeName(employee.getEmployeeName())
                    .idNumber(employee.getIdNumber())
                    .mobile(StringUtils.isNotEmpty(employee.getMobile()) ? employee.getMobile() : "-")
                    .remark(StringUtils.isNotEmpty(employee.getRemark()) ? employee.getRemark() : "-")
                    .socialInsuranceBase(formatSocialInsuranceBase(employee.getGrossSalary()))
                    .build();

            // 解析社保信息
            parseSocialInsurance(employee.getSocialInsurance(), exportDTO);

            exportList.add(exportDTO);
        }

        log.info("Found {} social insurance records for export", exportList.size());
        return exportList;
    }

    /**
     * 获取个税明细数据用于Excel导出
     *
     * @param deliveryOrderNo 交付单编号
     * @return 个税明细导出数据列表
     */
    public List<PersonalTaxDetailExportDTO> getPersonalTaxDetailForExport(String deliveryOrderNo) {
        log.info("Getting personal tax detail for export, deliveryOrderNo: {}", deliveryOrderNo);

        // 查询个税明细业务类型的员工数据
        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                   .eq(ValueAddedEmployee::getBizType, ValueAddedBizType.PERSONAL_TAX.getCode()); // 个税明细

        List<ValueAddedEmployee> employees = this.list(queryWrapper);
        List<PersonalTaxDetailExportDTO> exportList = new ArrayList<>();

        for (ValueAddedEmployee employee : employees) {
            PersonalTaxDetailExportDTO exportDTO = PersonalTaxDetailExportDTO.builder()
                    .operationType(convertOperationType(employee.getOperationType()))
                    .employeeName(employee.getEmployeeName())
                    .idNumber(employee.getIdNumber())
                    .mobile(StringUtils.isNotEmpty(employee.getMobile()) ? employee.getMobile() : "-")
                    .remark(StringUtils.isNotEmpty(employee.getRemark()) ? employee.getRemark() : "-")
                    .socialInsuranceBase(formatSocialInsuranceBase(employee.getGrossSalary()))
                    .grossSalary(employee.getGrossSalary() != null ? employee.getGrossSalary().toString() : "-")
                    .housingFundPersonalAmount(employee.getProvidentFundPersonal() != null ? employee.getProvidentFundPersonal().toString() : "-")
                    .build();

            // 解析社保信息（个税明细现在也使用 socialInsurance 字段）
            parseSocialInsurance(employee.getSocialInsurance(), exportDTO);

            // 解析扩展信息中的其他字段
            parsePersonalTaxExtendInfo(employee.getExtendInfo(), exportDTO);

            exportList.add(exportDTO);
        }

        log.info("Found {} personal tax records for export", exportList.size());
        return exportList;
    }

    @Override
    public void processBatchEmployeeData(Long fileId, Integer bizType, Boolean overrideExisting) {
        log.info("Starting process batch employee data, fileId: {}, bizType: {}", fileId, bizType);

        // 异步处理数据
        CompletableFuture.runAsync(() -> {
            processBatchDataFromFile(fileId, bizType, overrideExisting);
        });
    }

    @Override
    public String getProcessStatusByFileId(Long fileId) {
        return valueAddedFileService.getFileProcessStatus(fileId);
    }

    /**
     * 从文件异步处理批量数据
     * deliveryOrderNo从文件记录中获取
     */
    private void processBatchDataFromFile(Long fileId, Integer bizType, Boolean overrideExisting) {
        log.info("Processing batch data from file, fileId: {}, bizType: {}", fileId, bizType);

        int totalCount = 0;
        int successCount = 0;
        int failCount = 0;

        try {
            // 1. 获取文件信息
            ValueAddedFile fileRecord = valueAddedFileService.getFileById(fileId);
            if (fileRecord == null) {
                throw new RuntimeException("文件记录不存在");
            }

            // 获取交付单编号
            String deliveryOrderNo = fileRecord.getDeliveryOrderNo();
            log.info("Processing batch data with deliveryOrderNo: {}", deliveryOrderNo);

            // 2. 更新状态为处理中
            updateFileStatus(fileId, ValueAddedProcessStatus.PROCESSING.getCode(), "开始解析Excel文件", 0, 0, 0);
            // 3. 下载并解析Excel文件
            String fullFileUrl = remoteFileService.getFullFileUrl(fileRecord.getFileUrl()).getDataThrowException();
            if (StringUtils.isEmpty(fullFileUrl)) {
                throw new RuntimeException("获取文件下载地址失败");
            }

            // 4. 解析Excel文件
            List<?> importList = parseExcelFromUrl(fullFileUrl, bizType);
            if (importList == null || importList.isEmpty()) {
                throw new RuntimeException("Excel文件中没有有效数据");
            }

            totalCount = importList.size();
            updateFileStatus(fileId, ValueAddedProcessStatus.PROCESSING.getCode(), "开始处理员工数据", totalCount, 0, 0);

            // 5. 处理每条员工数据
            for (int i = 0; i < importList.size(); i++) {
                Object importDTO = importList.get(i);

                try {
                    // 设置行号用于错误定位
                    setRowNumber(importDTO, i + 2); // Excel行号从2开始（第1行是标题）

                    // 转换为ValueAddedEmployeeVO
                    ValueAddedEmployeeVO employeeVO = convertToEmployeeVO(importDTO, bizType, deliveryOrderNo);

                    // 调用现有的upsert方法
                    boolean success = upsert(employeeVO);

                    if (success) {
                        successCount++;
                    } else {
                        log.error("Process single record failed, row: {}, employee: {}, error: 保存失败", i + 2, getEmployeeName(importDTO));
                        failCount++;
                    }
                } catch (Exception e) {
                    log.error("Process single record failed, row: {}, employee: {}, error: {}", i + 2, getEmployeeName(importDTO), e.getMessage());
                    failCount++;
                }

                // 更新进度（每处理10条记录更新一次状态，减少数据库操作）
                int processedCount = i + 1;
                if (processedCount % 10 == 0 || processedCount == totalCount) {
                    updateFileStatus(fileId, ValueAddedProcessStatus.PROCESSING.getCode(), "正在处理数据", totalCount, successCount, failCount);
                }
            }

            // 6. 处理完成
            updateFileStatus(fileId, ValueAddedProcessStatus.COMPLETED.getCode(), "处理完成", totalCount, successCount, failCount);
            log.info("Batch processing completed, fileId: {}, success: {}, fail: {}", fileId, successCount, failCount);

        } catch (Exception e) {
            log.error("Batch processing failed, fileId: {}", fileId, e);
            updateFileStatus(fileId, ValueAddedProcessStatus.FAILED.getCode(), "处理失败: " + e.getMessage(), totalCount, successCount, failCount);
        }
    }

    /**
     * 更新文件处理状态
     */
    private void updateFileStatus(Long fileId, Integer status, String message,
                                 Integer totalCount, Integer successCount, Integer failCount) {
        try {
            // 使用DTO封装状态信息，便于使用BeanUtils
            FileStatusInfo statusInfo = FileStatusInfo.builder()
                    .status(status)
                    .message(message)
                    .totalCount(totalCount)
                    .successCount(successCount)
                    .failCount(failCount)
                    .hasErrorFile(failCount != null && failCount > 0)
                    .updateTime(System.currentTimeMillis())
                    .build();

            // 直接将DTO对象序列化为JSON - 使用FastJSON2替代ObjectMapper
            String statusJson = JSON.toJSONString(statusInfo);

            valueAddedFileService.updateFileProcessStatus(fileId, status, statusJson);
        } catch (Exception e) {
            log.error("Update file status failed, fileId: {}", fileId, e);
        }
    }

    /**
     * 从URL解析Excel文件（根据业务类型选择不同的DTO）
     */
    private List<?> parseExcelFromUrl(String fileUrl, Integer bizType) throws Exception {
        try (InputStream inputStream = new URL(fileUrl).openStream()) {
            if (bizType == ValueAddedBizType.SOCIAL_INSURANCE.getCode()) {
                // 社医保使用SocialInsuranceDTO
                ExcelUtil<SocialInsuranceDTO> util = new ExcelUtil<>(SocialInsuranceDTO.class);
                return util.importExcel(inputStream);
            } else if (bizType == ValueAddedBizType.PERSONAL_TAX.getCode()) {
                // 个税明细使用PersonalTaxDetailImportDTO
                ExcelUtil<PersonalTaxDetailImportDTO> util = new ExcelUtil<>(PersonalTaxDetailImportDTO.class);
                return util.importExcel(inputStream);
            } else {
                throw new IllegalArgumentException("不支持的业务类型: " + bizType);
            }
        }
    }

    /**
     * 从URL解析Excel文件（兼容旧版本）
     */
    private List<SocialInsuranceDTO> parseExcelFromUrl(String fileUrl) throws Exception {
        try (InputStream inputStream = new URL(fileUrl).openStream()) {
            ExcelUtil<SocialInsuranceDTO> util = new ExcelUtil<>(SocialInsuranceDTO.class);
            return util.importExcel(inputStream);
        }
    }

    /**
     * 将导入DTO转换为员工VO（支持多种业务类型）
     */
    private ValueAddedEmployeeVO convertToEmployeeVO(Object importDTO, Integer bizType, String deliveryOrderNo) {
        if (bizType == ValueAddedBizType.SOCIAL_INSURANCE.getCode()) {
            return convertSocialInsuranceToEmployeeVO((SocialInsuranceDTO) importDTO, bizType, deliveryOrderNo);
        } else if (bizType == ValueAddedBizType.PERSONAL_TAX.getCode()) {
            return convertPersonalTaxToEmployeeVO((PersonalTaxDetailImportDTO) importDTO, bizType, deliveryOrderNo);
        } else {
            throw new IllegalArgumentException("不支持的业务类型: " + bizType);
        }
    }

    /**
     * 将社保导入DTO转换为员工VO
     * 使用BeanUtils优化字段复制，提高代码可维护性
     */
    private ValueAddedEmployeeVO convertSocialInsuranceToEmployeeVO(SocialInsuranceDTO importDTO, Integer bizType, String deliveryOrderNo) {
        // 验证必填字段
        validateImportDTO(importDTO);

        // 创建目标VO对象
        ValueAddedEmployeeVO employeeVO = new ValueAddedEmployeeVO();

        // 使用BeanUtils复制同名字段（如employeeName, idNumber, mobile, remark等）
        BeanUtils.copyProperties(importDTO, employeeVO);
        employeeVO.setBizType(bizType != null ? bizType : ValueAddedBizType.SOCIAL_INSURANCE.getCode());
        employeeVO.setEntryType(ValueAddedEntryType.BATCH_ADD.getCode());
        employeeVO.setStatus(ValueAddedEmployeeStatus.PENDING.getCode());

        // 设置交付单编号
        employeeVO.setDeliveryOrderNo(deliveryOrderNo);
        log.debug("Setting deliveryOrderNo for employee: {}, deliveryOrderNo: {}", importDTO.getEmployeeName(), deliveryOrderNo);

        // 处理需要转换的字段
        employeeVO.setOperationType(convertOperationTypeFromString(importDTO.getOperationType()));
        employeeVO.setGrossSalary(convertSocialInsuranceBase(importDTO.getSocialInsuranceBase()));
        employeeVO.setSocialInsurance(buildSocialInsurance(importDTO));

        // 清理字符串字段的空白字符
        trimStringFields(employeeVO);

        return employeeVO;
    }

    /**
     * 将个税明细导入DTO转换为员工VO
     */
    private ValueAddedEmployeeVO convertPersonalTaxToEmployeeVO(PersonalTaxDetailImportDTO importDTO, Integer bizType, String deliveryOrderNo) {
        // 验证必填字段
        validatePersonalTaxImportDTO(importDTO);

        // 创建目标VO对象
        ValueAddedEmployeeVO employeeVO = new ValueAddedEmployeeVO();

        // 使用BeanUtils复制同名字段（如employeeName, idNumber, mobile, remark等）
        BeanUtils.copyProperties(importDTO, employeeVO);
        employeeVO.setBizType(bizType);
        employeeVO.setEntryType(ValueAddedEntryType.BATCH_ADD.getCode());
        employeeVO.setStatus(ValueAddedEmployeeStatus.PENDING.getCode());

        // 设置交付单编号
        employeeVO.setDeliveryOrderNo(deliveryOrderNo);

        // 处理需要转换的字段
        employeeVO.setOperationType(convertOperationTypeFromString(importDTO.getOperationType()));

        // 个税明细特有字段处理
        employeeVO.setGrossSalary(convertStringToBigDecimal(importDTO.getGrossSalary()));
        employeeVO.setProvidentFundPersonal(convertStringToBigDecimal(importDTO.getHousingFundPersonalAmount()));

        // 构建社保信息JSON字符串（统一使用 socialInsurance 字段）
        String socialInsurance = buildPersonalTaxSocialInsurance(importDTO);
        employeeVO.setSocialInsurance(socialInsurance);

        // 构建个税明细扩展信息JSON字符串（不再包含社保信息）
        employeeVO.setExtendInfo(buildPersonalTaxExtendInfo(importDTO));

        // 清理字符串字段的空白字符
        trimStringFields(employeeVO);

        return employeeVO;
    }

    /**
     * 验证导入DTO的必填字段
     */
    private void validateImportDTO(SocialInsuranceDTO importDTO) {
        if (StringUtils.isEmpty(importDTO.getEmployeeName())) {
            throw new IllegalArgumentException("员工姓名不能为空");
        }
        if (StringUtils.isEmpty(importDTO.getIdNumber())) {
            throw new IllegalArgumentException("身份证号不能为空");
        }
        if (StringUtils.isEmpty(importDTO.getOperationType())) {
            throw new IllegalArgumentException("操作方式不能为空");
        }
    }

    /**
     * 验证个税明细导入DTO的必填字段
     */
    private void validatePersonalTaxImportDTO(PersonalTaxDetailImportDTO importDTO) {
        if (StringUtils.isEmpty(importDTO.getEmployeeName())) {
            throw new IllegalArgumentException("员工姓名不能为空");
        }
        if (StringUtils.isEmpty(importDTO.getIdNumber())) {
            throw new IllegalArgumentException("身份证号不能为空");
        }
        if (StringUtils.isEmpty(importDTO.getOperationType())) {
            throw new IllegalArgumentException("操作方式不能为空");
        }
    }

    /**
     * 转换字符串为BigDecimal（通用方法）
     */
    private BigDecimal convertStringToBigDecimal(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("数值格式不正确: " + value);
        }
    }

    /**
     * 转换社保基数字符串为BigDecimal
     */
    private BigDecimal convertSocialInsuranceBase(String socialInsuranceBase) {
        if (StringUtils.isEmpty(socialInsuranceBase)) {
            return null;
        }
        try {
            return new BigDecimal(socialInsuranceBase);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("社保基数格式不正确: " + socialInsuranceBase);
        }
    }

    /**
     * 清理字符串字段的空白字符
     */
    private void trimStringFields(ValueAddedEmployeeVO employeeVO) {
        if (StringUtils.isNotEmpty(employeeVO.getEmployeeName())) {
            employeeVO.setEmployeeName(employeeVO.getEmployeeName().trim());
        }
        if (StringUtils.isNotEmpty(employeeVO.getIdNumber())) {
            employeeVO.setIdNumber(employeeVO.getIdNumber().trim());
        }
        if (StringUtils.isNotEmpty(employeeVO.getMobile())) {
            employeeVO.setMobile(employeeVO.getMobile().trim());
        }
        if (StringUtils.isNotEmpty(employeeVO.getRemark())) {
            employeeVO.setRemark(employeeVO.getRemark().trim());
        }
    }

    /**
     * 转换操作方式字符串为数字
     */
    private Integer convertOperationTypeFromString(String operationType) {
        if (StringUtils.isEmpty(operationType)) {
            throw new IllegalArgumentException("操作方式不能为空");
        }

        String trimmed = operationType.trim();
        switch (trimmed) {
            case "增员":
                return ValueAddedOperationType.REMIND.getCode();
            case "更正":
                return ValueAddedOperationType.CORRECTION.getCode();
            case "减员":
                return ValueAddedOperationType.REDUCTION.getCode();
            default:
                throw new IllegalArgumentException("不支持的操作方式: " + operationType + "，支持的方式：增员、更正、减员");
        }
    }

    /**
     * 构建社保JSON字符串
     */
    private String buildSocialInsurance(SocialInsuranceDTO importDTO) {
        Map<String, Boolean> packageMap = new HashMap<>();

        packageMap.put("yang_lao", convertBooleanFromString(importDTO.getYangLao()));
        packageMap.put("shi_ye", convertBooleanFromString(importDTO.getShiYe()));
        packageMap.put("gong_shang", convertBooleanFromString(importDTO.getGongShang()));
        packageMap.put("yi_liao", convertBooleanFromString(importDTO.getYiLiao()));
        packageMap.put("sheng_yu", convertBooleanFromString(importDTO.getShengYu()));

        try {
            // 使用FastJSON2替代ObjectMapper
            return JSON.toJSONString(packageMap);
        } catch (Exception e) {
            log.error("Build social insurance failed", e);
            throw new RuntimeException("构建社保信息失败: " + e.getMessage());
        }
    }

    /**
     * 构建个税明细社保信息JSON字符串
     */
    private String buildPersonalTaxSocialInsurance(PersonalTaxDetailImportDTO importDTO) {
        Map<String, Boolean> insuranceMap = new HashMap<>();

        // 将字符串格式转换为布尔值格式，与社医保业务保持一致
        insuranceMap.put("yang_lao", convertBooleanFromString(importDTO.getYangLao()));
        insuranceMap.put("shi_ye", convertBooleanFromString(importDTO.getShiYe()));
        insuranceMap.put("gong_shang", convertBooleanFromString(importDTO.getGongShang()));
        insuranceMap.put("yi_liao", convertBooleanFromString(importDTO.getYiLiao()));
        insuranceMap.put("sheng_yu", convertBooleanFromString(importDTO.getShengYu()));
        insuranceMap.put("qi_ta", false); // 默认其他为false

        try {
            // 使用FastJSON2替代ObjectMapper
            return JSON.toJSONString(insuranceMap);
        } catch (Exception e) {
            log.error("Build personal tax social insurance failed", e);
            throw new RuntimeException("构建个税明细社保信息失败: " + e.getMessage());
        }
    }

    /**
     * 构建个税明细扩展信息JSON字符串（不再包含社保信息）
     */
    private String buildPersonalTaxExtendInfo(PersonalTaxDetailImportDTO importDTO) {
        Map<String, Object> extendMap = new HashMap<>();

        // 添加个税明细特有字段
        extendMap.put("housing_fund_personal_amount", importDTO.getHousingFundPersonalAmount());
        extendMap.put("other", importDTO.getOther());

        // 不再添加保险信息，社保信息统一存储在 socialInsurance 字段中

        try {
            // 使用FastJSON2替代ObjectMapper
            return JSON.toJSONString(extendMap);
        } catch (Exception e) {
            log.error("Build personal tax extend info failed", e);
            throw new RuntimeException("构建个税明细扩展信息失败: " + e.getMessage());
        }
    }

    /**
     * 转换字符串为布尔值
     */
    private Boolean convertBooleanFromString(String value) {
        if (StringUtils.isEmpty(value)) {
            return false;
        }

        String trimmed = value.trim();
        return "是".equals(trimmed) || "true".equalsIgnoreCase(trimmed) || "1".equals(trimmed);
    }

    /**
     * 转换操作方式为中文显示
     */
    private String convertOperationType(Integer operationType) {
        if (operationType == null) {
            return "-";
        }
        switch (operationType) {
            case 1:
                return "增员";
            case 2:
                return "更正";
            case 3:
                return "减员";
            default:
                return "-";
        }
    }

    /**
     * 格式化社保基数
     */
    private String formatSocialInsuranceBase(BigDecimal grossSalary) {
        if (grossSalary == null) {
            return "-";
        }
        return grossSalary.toString();
    }

    /**
     * 解析社保信息
     * 使用FastJSON2替代ObjectMapper
     */
    private void parseSocialInsurance(String socialInsurance,
                                           SocialInsuranceDTO exportDTO) {
        // 设置默认值
        exportDTO.setYangLao("-");
        exportDTO.setShiYe("-");
        exportDTO.setGongShang("-");
        exportDTO.setYiLiao("-");
        exportDTO.setShengYu("-");

        if (StringUtils.isEmpty(socialInsurance)) {
            return;
        }

        try {
            // 使用FastJSON2替代ObjectMapper
            @SuppressWarnings("unchecked")
            Map<String, Boolean> insuranceMap = JSON.parseObject(socialInsurance, Map.class);

            exportDTO.setYangLao(Boolean.TRUE.equals(insuranceMap.get("yang_lao")) ? "是" : "否");
            exportDTO.setShiYe(Boolean.TRUE.equals(insuranceMap.get("shi_ye")) ? "是" : "否");
            exportDTO.setGongShang(Boolean.TRUE.equals(insuranceMap.get("gong_shang")) ? "是" : "否");
            exportDTO.setYiLiao(Boolean.TRUE.equals(insuranceMap.get("yi_liao")) ? "是" : "否");
            exportDTO.setShengYu(Boolean.TRUE.equals(insuranceMap.get("sheng_yu")) ? "是" : "否");
        } catch (Exception e) {
            log.warn("Parse social insurance failed: {}", socialInsurance, e);
        }
    }

    /**
     * 解析个税明细扩展信息
     */
    private void parsePersonalTaxExtendInfo(String extendInfo, PersonalTaxDetailExportDTO exportDTO) {
        if (StringUtils.isEmpty(extendInfo)) {
            exportDTO.setOther("-");
            return;
        }

        try {
            // 使用FastJSON2解析扩展信息
            @SuppressWarnings("unchecked")
            Map<String, Object> extendMap = JSON.parseObject(extendInfo, Map.class);

            // 解析其他字段
            String other = (String) extendMap.get("other");
            exportDTO.setOther(StringUtils.isNotEmpty(other) ? other : "-");

        } catch (Exception e) {
            log.warn("Parse personal tax extend info failed: {}", extendInfo, e);
            exportDTO.setOther("-");
        }
    }

    /**
     * 设置行号（通用方法）
     */
    private void setRowNumber(Object importDTO, Integer rowNumber) {
        if (importDTO instanceof SocialInsuranceDTO) {
            ((SocialInsuranceDTO) importDTO).setRowNumber(rowNumber);
        } else if (importDTO instanceof PersonalTaxDetailImportDTO) {
            ((PersonalTaxDetailImportDTO) importDTO).setRowNumber(rowNumber);
        }
    }

    /**
     * 获取员工姓名（通用方法）
     */
    private String getEmployeeName(Object importDTO) {
        if (importDTO instanceof SocialInsuranceDTO) {
            return ((SocialInsuranceDTO) importDTO).getEmployeeName();
        } else if (importDTO instanceof PersonalTaxDetailImportDTO) {
            return ((PersonalTaxDetailImportDTO) importDTO).getEmployeeName();
        }
        return "未知";
    }
}
